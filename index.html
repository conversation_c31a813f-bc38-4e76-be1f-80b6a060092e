<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="author" content="YiXi">
  <title><?php echo $conf['sitename']?> - 未来无限</title>
  <meta name="keywords" content="<?php echo $conf['keywords'];?>提供应用管理，API数据接口调用服务平台 - 我们致力于为开发者提供稳定、高效的API数据接口服务。">
  <meta name="description" content="<?php echo $conf['description']?>提供应用管理，API数据接口调用服务平台 - 我们致力于为开发者提供稳定、高效的API数据接口服务。">
  <link rel="shortcut icon" href="assets/img/favicon.ico" />
<link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/css/bootstrap.min.css">
<link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/animate.css/4.1.1/animate.min.css">
<link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/layui/2.6.8/css/layui.css">
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/jquery/3.6.0/jquery.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/layui/2.6.8/layui.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/gsap/3.9.1/gsap.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/three.js/r128/three.min.js"></script>
<script src="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aos/2.3.4/aos.js"></script>
<link rel="stylesheet" href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-1-M/aos/2.3.4/aos.css">
</head>

<style>
  :root {
    --primary-color: #0071e3;
    --secondary-color: #86c5ff;
    --accent-color: #34c759;
    --text-color: #1d1d1f;
    --light-text: #86868b;
    --background-color: #f5f5f7;
    --card-bg: rgba(255, 255, 255, 0.8);
    --glass-effect: rgba(255, 255, 255, 0.7);
    --shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --border-radius: 16px;
  }

  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  }

  body {
    background-color: var(--background-color);
    color: var(--text-color);
    overflow-x: hidden;
  }

  /* 背景动画 */
  .background-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
  }

  .background-gradient {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #f0f2f5 0%, #e6f0ff 50%, #f0f7ff 100%);
  }

  .floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    opacity: 0.1;
    filter: blur(40px);
  }

  /* 顶部状态栏样式 */
  .top-status-bar {
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding: 8px 0;
    font-size: 0.85rem;
    color: var(--light-text);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1001;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }

  .status-items {
    display: flex;
    gap: 20px;
  }

  .status-item {
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
  }

  .status-item:hover {
    transform: translateY(-2px);
    color: var(--primary-color);
  }

  .status-item i {
    color: var(--primary-color);
    font-size: 0.9rem;
  }

  .status-good {
    color: var(--accent-color);
    font-weight: 500;
  }

  .status-social {
    display: flex;
    gap: 15px;
  }

  .social-icon {
    color: var(--light-text);
    transition: all 0.3s ease;
  }

  .social-icon:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
  }

  .top-status-bar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .fps-counter {
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--primary-color);
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.8rem;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .fps-counter i {
    color: var(--accent-color);
    font-size: 0.8rem;
  }

  /* 导航栏样式优化 */
  .navbar {
    background-color: var(--glass-effect);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding: 0.8rem 0;
    position: fixed;
    width: 100%;
    top: 36px;
    z-index: 1000;
    transition: all 0.3s ease;
  }

  .brand-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .brand-icon {
    font-size: 1.8rem;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
  }

  .brand-text {
    display: flex;
    flex-direction: column;
  }

  .brand-name {
    font-weight: 700;
    font-size: 1.5rem;
    line-height: 1.2;
  }

  .brand-slogan {
    font-size: 0.75rem;
    color: var(--light-text);
    letter-spacing: 1px;
  }

  .dropdown-menu {
    background-color: var(--glass-effect);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    box-shadow: var(--shadow);
    padding: 0.5rem;
    min-width: 200px;
    margin-top: 10px;
  }

  .dropdown-item {
    padding: 0.6rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    color: var(--text-color);
  }

  .dropdown-item:hover {
    background-color: rgba(0, 113, 227, 0.1);
    color: var(--primary-color);
    transform: translateX(5px);
  }

  .dropdown-divider {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    margin: 0.5rem 0;
  }

  .highlight-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white !important;
    border-radius: 2rem;
    padding: 0.5rem 1.2rem !important;
    margin-left: 10px;
    box-shadow: 0 4px 12px rgba(0, 113, 227, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
  }

  .highlight-btn:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 6px 16px rgba(0, 113, 227, 0.4);
    background: linear-gradient(135deg, #0062cc, #5e9eff);
  }

  .highlight-text {
    font-weight: 600;
  }

  /* 主内容区 */
  .main-content {
    padding-top: 120px;
  }

  .hero-section {
    padding: 4rem 0;
    position: relative;
    overflow: hidden;
  }

  .hero-content {
    position: relative;
    z-index: 2;
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: fadeInUp 1s ease;
  }

  .hero-subtitle {
    font-size: 1.5rem;
    color: var(--light-text);
    margin-bottom: 2rem;
    max-width: 600px;
    animation: fadeInUp 1.2s ease;
  }

  .hero-features {
    margin-bottom: 2rem;
    animation: fadeInUp 1.4s ease;
  }

  .feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
  }

  .feature-item i {
    color: var(--accent-color);
    margin-right: 0.5rem;
  }

  .hero-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    animation: fadeInUp 1.6s ease;
  }

  .btn-primary {
    background-color: var(--primary-color);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 113, 227, 0.3);
  }

  .btn-primary:hover {
    background-color: #0062cc;
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 113, 227, 0.4);
  }

  .btn-outline {
    background-color: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
  }

  .btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 113, 227, 0.2);
  }

  /* 特性部分 */
  .features-section {
    padding: 6rem 0;
    position: relative;
  }

  .section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: var(--text-color);
  }

  .section-subtitle {
    text-align: center;
    font-size: 1.2rem;
    color: var(--light-text);
    max-width: 700px;
    margin: 0 auto 4rem;
    line-height: 1.6;
  }

  .feature-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.5);
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .feature-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      45deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transform: rotate(45deg);
    transition: all 0.8s ease;
    opacity: 0;
  }

  .feature-card:hover::before {
    animation: shine 1.5s ease;
  }

  @keyframes shine {
    0% {
      opacity: 0;
      transform: translateX(-100%) rotate(45deg);
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0;
      transform: translateX(100%) rotate(45deg);
    }
  }

  .feature-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 10;
  }

  .feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    transition: all 0.5s ease;
  }

  .feature-card:hover .feature-icon {
    transform: scale(1.2) rotate(5deg);
  }

  .feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-color);
    transition: all 0.3s ease;
  }

  .feature-card:hover .feature-title {
    color: var(--primary-color);
  }

  .feature-description {
    color: var(--light-text);
    line-height: 1.6;
    transition: all 0.3s ease;
  }

  /* 技术与创新区域 */
  .tech-innovation-section {
    padding: 6rem 0;
    background: linear-gradient(180deg, var(--background-color) 0%, rgba(230, 240, 255, 0.5) 100%);
    position: relative;
    overflow: hidden;
  }

  .tech-innovation-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB4PSIwIiB5PSIwIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgzMCkiPjxyZWN0IHg9IjAiIHk9IjAiIHdpZHRoPSIyIiBoZWlnaHQ9IjIiIGZpbGw9IiMwMDcxZTMiIG9wYWNpdHk9IjAuMDUiLz48L3BhdHRlcm4+PC9kZWZzPjxyZWN0IHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiIGZpbGw9InVybCgjcGF0dGVybikiLz48L3N2Zz4=');
    opacity: 0.5;
  }

  .tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 50px;
  }

  .tech-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(255, 255, 255, 0.5);
  }

  .tech-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .tech-icon-container {
    padding: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, rgba(0, 113, 227, 0.05), rgba(134, 197, 255, 0.1));
    position: relative;
    overflow: hidden;
  }

  .tech-icon {
    font-size: 3rem;
    color: var(--primary-color);
    position: relative;
    z-index: 2;
    transition: all 0.5s ease;
  }

  .tech-card:hover .tech-icon {
    transform: scale(1.2) rotate(10deg);
    color: #0062cc;
  }

  .tech-icon-container::after {
    content: '';
    position: absolute;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(0, 113, 227, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
    opacity: 0;
    transition: all 0.5s ease;
  }

  .tech-card:hover .tech-icon-container::after {
    opacity: 1;
    width: 200px;
    height: 200px;
  }

  .tech-content {
    padding: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .tech-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: var(--text-color);
    text-align: center;
  }

  .tech-description {
    color: var(--light-text);
    line-height: 1.6;
    text-align: center;
    flex-grow: 1;
  }

  /* 图标展示区 */
  .icons-showcase {
    padding: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .icons-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 20px;
    margin: 3rem auto;
    max-width: 900px;
  }

  .icon-item {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--card-bg);
    border-radius: 20px;
    box-shadow: var(--shadow);
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform-style: preserve-3d;
    position: relative;
    margin: 0 auto;
  }

  .icon-item:hover {
    transform: translateY(-10px) rotateY(10deg);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  }

  .icon-item i {
    font-size: 2rem;
    color: var(--primary-color);
    transition: all 0.3s ease;
  }

  .icon-item:hover i {
    transform: scale(1.2);
    color: #0062cc;
  }

  .showcase-message {
    font-size: 1.2rem;
    color: var(--primary-color);
    font-weight: 600;
    margin-top: 2rem;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  .icons-container:hover + .showcase-message {
    opacity: 1;
  }

  /* 页脚样式优化 */
  .footer-area {
    position: relative;
    background-color: #f8f9fa;
    overflow: hidden;
    margin-top: 6rem;
  }

  .footer-wave-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    overflow: hidden;
  }

  .footer-wave {
    position: absolute;
    top: -25px;
    left: 0;
    width: 100%;
    height: 100px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNDQwIDMyMCI+PHBhdGggZmlsbD0iI2Y4ZjlmYSIgZmlsbC1vcGFjaXR5PSIxIiBkPSJNMCwzMjBMMTIwLDI4OEMyNDAsMjU2LDQ4MCwxOTIsNzIwLDE5MkM5NjAsMTkyLDEyMDAsMjU2LDEzMjAsMjg4TDE0NDAsMzIwTDE0NDAsMzIwTDEzMjAsMzIwQzEyMDAsMzIwLDk2MCwzMjAsNzIwLDMyMEM0ODAsMzIwLDI0MCwzMjAsMTIwLDMyMEwwLDMyMFoiPjwvcGF0aD48L3N2Zz4=');
    background-size: 1440px 100px;
  }

  .footer-animation {
    animation: wave-animation 20s linear infinite;
  }

  @keyframes wave-animation {
    0% {
      background-position-x: 0;
    }
    100% {
      background-position-x: 1440px;
    }
  }

  .main-footer {
    padding: 5rem 0 3rem;
    position: relative;
  }

  .footer-widget {
    margin-bottom: 2rem;
  }

  .footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1.5rem;
  }

  .logo-icon {
    font-size: 2rem;
    color: var(--primary-color);
  }

  .footer-logo h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    color: var(--text-color);
  }

  .footer-about-text {
    color: var(--light-text);
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }

  .footer-social {
    display: flex;
    gap: 12px;
  }

  .social-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(0, 113, 227, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    transition: all 0.3s ease;
  }

  .social-circle:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-5px);
  }

  .footer-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-color);
    position: relative;
    padding-bottom: 10px;
  }

  .footer-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  }

  .footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  .footer-links li {
    margin-bottom: 10px;
  }

  .footer-links a {
    color: var(--light-text);
    text-decoration: none;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
  }

  .footer-links a i {
    font-size: 0.7rem;
    margin-right: 8px;
    transition: all 0.3s ease;
  }

  .footer-links a:hover {
    color: var(--primary-color);
    transform: translateX(5px);
  }

  .footer-links a:hover i {
    color: var(--primary-color);
  }

  .contact-info-item {
    display: flex;
    margin-bottom: 15px;
  }

  .contact-info-icon {
    width: 40px;
    height: 40px;
    background-color: rgba(0, 113, 227, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    margin-right: 15px;
  }

  .contact-info-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .contact-info-text span {
    color: var(--light-text);
  }

  .footer-newsletter {
    margin-top: 20px;
  }

  .footer-newsletter h5 {
    font-size: 1rem;
    margin-bottom: 15px;
    color: var(--text-color);
  }

  .newsletter-form {
    display: flex;
    position: relative;
  }

  .newsletter-form input {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 2rem;
    background-color: white;
    outline: none;
    transition: all 0.3s ease;
  }

  .newsletter-form input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.1);
  }

  .newsletter-form button {
    position: absolute;
    right: 5px;
    top: 5px;
    bottom: 5px;
    width: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .newsletter-form button:hover {
    transform: scale(1.1);
  }

  .footer-bottom {
    padding: 1.5rem 0;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }

  .footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
  }

  .copyright {
    color: var(--light-text);
  }

  .copyright a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
  }

  .footer-bottom-links {
    display: flex;
    gap: 20px;
  }

  .footer-bottom-links a {
    color: var(--light-text);
    text-decoration: none;
    transition: all 0.3s ease;
  }

  .footer-bottom-links a:hover {
    color: var(--primary-color);
  }

  .runtime-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
  }

  .runtime-icon {
    color: #ff5e5e;
  }

  .pulse {
    animation: pulse 1.5s infinite;
  }

  .runtime-text {
    color: var(--light-text);
  }

  /* 通知弹窗 */
  .notice-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 500px;
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    z-index: 9999;
    overflow: hidden;
    padding: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.5s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  }

  .notice-modal.show {
    opacity: 1;
    visibility: visible;
  }

  .notice-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    padding: 1.5rem;
    color: white;
    text-align: center;
    position: relative;
  }

  .notice-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
  }

  .notice-body {
    padding: 2rem;
    text-align: center;
  }

  .notice-content {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: var(--text-color);
  }

  .notice-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .notice-btn {
    padding: 1rem;
    border-radius: 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    font-size: 1rem;
  }

  .notice-btn-primary {
    background-color: var(--primary-color);
    color: white;
  }

  .notice-btn-primary:hover {
    background-color: #0062cc;
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 113, 227, 0.4);
  }

  .notice-btn-secondary {
    background-color: #e0e0e0;
    color: var(--text-color);
  }

  .notice-btn-secondary:hover {
    background-color: #d0d0d0;
    transform: translateY(-3px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  }

  /* 心形点击效果 */
  .heart-animation {
    position: absolute;
    pointer-events: none;
    z-index: 9999;
    font-size: 1.5rem;
    animation: float-up 1.5s forwards;
  }

  @keyframes float-up {
    0% {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
    100% {
      opacity: 0;
      transform: translateY(-100px) scale(1.5);
    }
  }

  /* 客服按钮 */
  .customer-service {
    position: fixed;
    right: 20px;
    bottom: 20px;
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 113, 227, 0.5);
    cursor: pointer;
    z-index: 100;
    transition: all 0.3s ease;
  }

  .customer-service:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(0, 113, 227, 0.6);
  }

  .customer-service i {
    font-size: 1.5rem;
  }

/* 手机端CSS优化 */
@media (max-width: 768px) {
  /* 按钮双排显示 */
  .hero-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    width: 100%;
  }
  
  .btn {
    padding: 0.6rem 0;
    font-size: 0.9rem;
    width: 100%;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  
  /* 快速链接和实用工具区域优化 */
  .quick-links-section,
  .tools-section {
    padding: 2rem 0;
  }
  
  .section-heading {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
    padding-bottom: 0.5rem;
    text-align: left;
  }
  
  .section-heading:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
  }
  
  .links-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 2rem;
  }
  
  .link-item {
    display: flex;
    align-items: center;
    padding: 0.8rem 1rem;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
  }
  
  .link-item:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateX(5px);
  }
  
  .link-item i {
    margin-right: 10px;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
  }
  
  /* 社交图标区域 */
  .social-icons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 1.5rem 0;
  }
  
  .social-icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 113, 227, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    transition: all 0.3s ease;
  }
  
  .social-icon-circle:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
  }
  
  /* 顶部状态栏优化 */
  .top-status-bar {
    padding: 5px 0;
  }
  
  .top-status-bar .container {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 5px;
  }
  
  .status-items {
    flex-wrap: wrap;
    gap: 8px;
    justify-content: center;
    width: 100%;
  }
  
  .status-item {
    font-size: 0.75rem;
  }
  
  /* 导航栏优化 */
  .navbar {
    top: 36px;
  }
  
  .navbar-brand {
    max-width: 70%;
  }
  
  /* 主内容区域优化 */
  .main-content {
    padding-top: 100px;
  }
  
  /* 特性列表优化 */
  .feature-list {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    margin: 1.5rem 0;
  }
  
  .feature-item-mobile {
    display: flex;
    align-items: center;
    padding: 0.8rem;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }
  
  .feature-icon-mobile {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(0, 113, 227, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: var(--primary-color);
  }
  
  .feature-text {
    font-size: 0.9rem;
  }
}

/* 超小屏幕优化 */
@media (max-width: 576px) {
  /* 按钮单排显示 */
  .hero-buttons {
    grid-template-columns: 1fr;
  }
  
  /* 社交图标调整 */
  .social-icons {
    gap: 10px;
  }
  
  .social-icon-circle {
    width: 36px;
    height: 36px;
  }
  
  /* 顶部状态栏调整 */
  .status-items {
    justify-content: flex-start;
    padding-left: 10px;
  }
  
  .navbar {
    top: 60px;
  }
  
  .main-content {
    padding-top: 130px;
  }
}

/* 超小屏幕额外优化 */
@media (max-width: 375px) {
  .status-item {
    font-size: 0.7rem;
  }
  
  .feature-text {
    font-size: 0.8rem;
  }
  
  .section-heading {
    font-size: 1.1rem;
  }
}

  /* 动画 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.05);
    }
    100% {
      transform: scale(1);
    }
  }

  .animate-pulse {
    animation: pulse 2s infinite;
  }

  /* AOS动画自定义 */
  [data-aos] {
    pointer-events: none;
  }
  [data-aos].aos-animate {
    pointer-events: auto;
  }
</style>

<body>
  <!-- 背景容器 -->
  <div class="background-container">
    <div class="background-gradient"></div>
    <div class="floating-shapes" id="floating-shapes"></div>
  </div>

  <!-- 顶部状态栏 -->
  <div class="top-status-bar">
    <div class="container">
      <div class="status-items">
        <div class="status-item">
          <i class="fas fa-server"></i>
          <span>服务器状态: <span class="status-good">正常</span></span>
        </div>
        <div class="status-item">
          <i class="fas fa-clock"></i>
          <span>当前时间: <span id="current-time"></span></span>
        </div>
        <div class="status-item">
          <i class="fas fa-chart-line"></i>
          <span>API调用: <span id="api-count">9,382,651</span></span>
        </div>
        <div class="status-item fps-counter">
          <i class="fas fa-tachometer-alt"></i>
          <span id="fps-display">60 FPS</span>
        </div>
      </div>
      <div class="status-social">
        <a href="#" class="social-icon"><i class="fab fa-weixin"></i></a>
        <a href="#" class="social-icon"><i class="fab fa-qq"></i></a>
        <a href="#" class="social-icon"><i class="fab fa-weibo"></i></a>
      </div>
    </div>
  </div>

  <!-- 导航栏 -->
  <nav class="navbar navbar-expand-lg">
    <div class="container">
      <a class="navbar-brand" href="/">
        <div class="brand-container">
          <div class="brand-icon"><i class="fas fa-cube"></i></div>
          <div class="brand-text">
            <span class="brand-name"><?php echo $conf['sitename']?></span>
            <span class="brand-slogan">创新科技 · 智慧未来</span>
          </div>
        </div>
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <i class="fas fa-bars"></i>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-user-circle me-2"></i>用户中心
            </a>
            <ul class="dropdown-menu" aria-labelledby="userDropdown">
              <li><a class="dropdown-item" href="/user/login.php"><i class="fas fa-sign-in-alt me-2"></i>用户登录</a></li>
              <li><a class="dropdown-item" href="/user/reg.php"><i class="fas fa-user-plus me-2"></i>注册账号</a></li>
              <li><a class="dropdown-item" href="/user/czpass.php"><i class="fas fa-key me-2"></i>找回密码</a></li>
              <li><hr class="dropdown-divider"></li>
              <li><a class="dropdown-item" href="/agent/login.php"><i class="fas fa-user-tie me-2"></i>代理登录</a></li>
            </ul>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="toolsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
              <i class="fas fa-tools me-2"></i>实用工具
            </a>
            <ul class="dropdown-menu" aria-labelledby="toolsDropdown">
              <li><a class="dropdown-item" href="/kmunmachine.php"><i class="fas fa-unlink me-2"></i>卡密解绑</a></li>
              <li><a class="dropdown-item" href="/km.php"><i class="fas fa-search me-2"></i>卡密查询</a></li>
            </ul>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/?mod=document"><i class="fas fa-book me-2"></i>开发文档</a>
          </li>
          <li class="nav-item">
            <a class="nav-link highlight-btn" href="/user/login.php">
              <span class="highlight-text">立即体验</span>
              <i class="fas fa-arrow-right ms-2"></i>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- 主内容区 -->
  <div class="main-content">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-6 hero-content">
            <h1 class="hero-title"><?php echo $conf['sitename']?></h1>
            <p class="hero-subtitle"><?php echo $conf['sitename']?> - 为开发者提供稳定、高效的API数据接口服务平台</p>
            
            <div class="hero-features">
              <div class="feature-item">
                <i class="fas fa-shield-alt"></i>
                <span>Rc4加密，保障数据安全</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-lock"></i>
                <span>DES加密，防止数据泄露</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-clock"></i>
                <span>时间戳校验，确保请求有效性</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-laptop-code"></i>
                <span>机器码验证，防止非法调用</span>
              </div>
              <div class="feature-item">
                <i class="fas fa-signature"></i>
                <span>双向签名验证，确保数据完整性</span>
              </div>
            </div>
            
            <div class="hero-buttons">
              <a href="/user/login.php" class="btn btn-primary"><i class="fas fa-sign-in-alt me-2"></i>用户登录</a>
              <a href="/agent/login.php" class="btn btn-primary"><i class="fas fa-user-tie me-2"></i>代理登录</a>
              <a href="/user/reg.php" class="btn btn-outline"><i class="fas fa-user-plus me-2"></i>注册账号</a>
              <a href="/user/czpass.php" class="btn btn-outline"><i class="fas fa-key me-2"></i>找回密码</a>
              <a href="/km.php" class="btn btn-outline"><i class="fas fa-search me-2"></i>卡密查询</a>
              <a href="/kmunmachine.php" class="btn btn-outline"><i class="fas fa-unlink me-2"></i>卡密解绑</a>
              <a href="/?mod=document" class="btn btn-outline"><i class="fas fa-book me-2"></i>开发文档</a>
              <a href="/user/agreement.php" class="btn btn-outline"><i class="fas fa-file-contract me-2"></i>服务协议</a>
            </div>
          </div>
          <div class="col-lg-6 d-none d-lg-block">
            <div class="hero-image" id="hero-3d-container">
              <!-- 3D动画将在这里渲染 -->
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 特性部分 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up"><?php echo $conf['sitename']?> 核心优势</h2>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">
          "我们趋行在人生这个亘古的旅途，在坎坷中奔跑，在挫折里涅槃，忧愁缠满全身，痛苦飘洒一地。我们累，却无从止歇；我们苦，却无法回避。"
        </p>
        
        <div class="row">
          <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="150">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <h3 class="feature-title">安全可靠</h3>
              <p class="feature-description">采用多重加密技术，确保您的数据安全，防止未授权访问和数据泄露。</p>
            </div>
          </div>
          
          <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="200">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-bolt"></i>
              </div>
              <h3 class="feature-title">高效稳定</h3>
              <p class="feature-description">优化的服务器架构和响应机制，确保API调用快速响应，稳定可靠。</p>
            </div>
          </div>
          
          <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="250">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-code"></i>
              </div>
              <h3 class="feature-title">简单集成</h3>
              <p class="feature-description">提供详细的API文档和示例代码，让您能够轻松集成我们的服务到您的应用中。</p>
            </div>
          </div>
          
          <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="300">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <h3 class="feature-title">实时监控</h3>
              <p class="feature-description">提供API调用统计和监控功能，让您随时了解应用的使用情况和性能表现。</p>
            </div>
          </div>
          
          <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="350">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-users"></i>
              </div>
              <h3 class="feature-title">专业支持</h3>
              <p class="feature-description">专业的技术团队提供全天候支持，解决您在使用过程中遇到的任何问题。</p>
            </div>
          </div>
          
          <div class="col-md-4 mb-4" data-aos="fade-up" data-aos-delay="400">
            <div class="feature-card">
              <div class="feature-icon">
                <i class="fas fa-sync-alt"></i>
              </div>
              <h3 class="feature-title">持续更新</h3>
              <p class="feature-description">我们不断更新和优化API服务，确保您始终能够使用最新、最好的功能。</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 技术与创新区域 (重新设计) -->
    <section class="tech-innovation-section">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">技术与创新</h2>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">每一个程序都承载着故事，感谢您选择我们</p>
        
        <div class="tech-grid">
          <div class="tech-card" data-aos="zoom-in" data-aos-delay="150">
            <div class="tech-icon-container">
              <i class="fas fa-shield-alt tech-icon"></i>
            </div>
            <div class="tech-content">
              <h3 class="tech-title">安全可靠</h3>
              <p class="tech-description">采用多重加密技术，确保您的数据安全，防止未授权访问和数据泄露。</p>
            </div>
          </div>
          
          <div class="tech-card" data-aos="zoom-in" data-aos-delay="200">
            <div class="tech-icon-container">
              <i class="fas fa-bolt tech-icon"></i>
            </div>
            <div class="tech-content">
              <h3 class="tech-title">高效稳定</h3>
              <p class="tech-description">优化的服务器架构和响应机制，确保API调用快速响应，稳定可靠。</p>
            </div>
          </div>
          
          <div class="tech-card" data-aos="zoom-in" data-aos-delay="250">
            <div class="tech-icon-container">
              <i class="fas fa-code tech-icon"></i>
            </div>
            <div class="tech-content">
              <h3 class="tech-title">简单集成</h3>
              <p class="tech-description">提供详细的API文档和示例代码，让您能够轻松集成我们的服务到您的应用中。</p>
            </div>
          </div>
          
          <div class="tech-card" data-aos="zoom-in" data-aos-delay="300">
            <div class="tech-icon-container">
              <i class="fas fa-chart-line tech-icon"></i>
            </div>
            <div class="tech-content">
              <h3 class="tech-title">实时监控</h3>
              <p class="tech-description">提供API调用统计和监控功能，让您随时了解应用的使用情况和性能表现。</p>
            </div>
          </div>
          
          <div class="tech-card" data-aos="zoom-in" data-aos-delay="350">
            <div class="tech-icon-container">
              <i class="fas fa-users tech-icon"></i>
            </div>
            <div class="tech-content">
              <h3 class="tech-title">专业支持</h3>
              <p class="tech-description">专业的技术团队提供全天候支持，解决您在使用过程中遇到的任何问题。</p>
            </div>
          </div>
          
          <div class="tech-card" data-aos="zoom-in" data-aos-delay="400">
            <div class="tech-icon-container">
              <i class="fas fa-sync-alt tech-icon"></i>
            </div>
            <div class="tech-content">
              <h3 class="tech-title">持续更新</h3>
              <p class="tech-description">我们不断更新和优化API服务，确保您始终能够使用最新、最好的功能。</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 图标展示区 -->
    <section class="icons-showcase">
      <div class="container">
        <h2 class="section-title" data-aos="fade-up">我们的技术栈</h2>
        <p class="section-subtitle" data-aos="fade-up" data-aos-delay="100">探索无限可能，创造无限价值</p>
        
        <div class="icons-container" data-aos="fade-up" data-aos-delay="200">
          <div class="icon-item"><i class="fas fa-cog"></i></div>
          <div class="icon-item"><i class="fas fa-database"></i></div>
          <div class="icon-item"><i class="fas fa-bell"></i></div>
          <div class="icon-item"><i class="fas fa-credit-card"></i></div>
          <div class="icon-item"><i class="fas fa-briefcase"></i></div>
          <div class="icon-item"><i class="fas fa-building"></i></div>
          <div class="icon-item"><i class="fas fa-play-circle"></i></div>
          <div class="icon-item"><i class="fas fa-calendar"></i></div>
          <div class="icon-item"><i class="fas fa-camera"></i></div>
          <div class="icon-item"><i class="fas fa-chart-bar"></i></div>
          <div class="icon-item"><i class="fas fa-cloud"></i></div>
          <div class="icon-item"><i class="fas fa-code-branch"></i></div>
        </div>
        
        <p class="showcase-message">探索无限可能</p>
      </div>
    </section>
  </div>

  <!-- 页脚 -->
  <footer class="footer-area">
    <div class="footer-wave-box">
      <div class="footer-wave footer-animation"></div>
    </div>
    
    <div class="main-footer">
      <div class="container">
        <div class="row">
          <div class="col-lg-3 col-md-6">
            <div class="footer-widget about-widget">
              <div class="footer-logo">
                <i class="fas fa-cube logo-icon"></i>
                <h3><?php echo $conf['sitename']?></h3>
              </div>
              <p class="footer-about-text">我们致力于为开发者提供稳定、高效的API数据接口服务，让您的应用开发更加便捷。</p>
              <div class="footer-social">
                <a href="#" class="social-circle"><i class="fab fa-weixin"></i></a>
                <a href="#" class="social-circle"><i class="fab fa-qq"></i></a>
                <a href="#" class="social-circle"><i class="fab fa-github"></i></a>
                <a href="#" class="social-circle"><i class="fab fa-weibo"></i></a>
              </div>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="footer-widget link-widget">
              <h4 class="footer-title">快速链接</h4>
              <ul class="footer-links">
                <li><a href="/"><i class="fas fa-chevron-right"></i> 首页</a></li>
                <li><a href="/user/login.php"><i class="fas fa-chevron-right"></i> 用户登录</a></li>
                <li><a href="/user/reg.php"><i class="fas fa-chevron-right"></i> 注册账号</a></li>
                <li><a href="/agent/login.php"><i class="fas fa-chevron-right"></i> 代理登录</a></li>
                <li><a href="/?mod=document"><i class="fas fa-chevron-right"></i> 开发文档</a></li>
              </ul>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="footer-widget link-widget">
              <h4 class="footer-title">实用工具</h4>
              <ul class="footer-links">
                <li><a href="/kmunmachine.php"><i class="fas fa-chevron-right"></i> 卡密解绑</a></li>
                <li><a href="/km.php"><i class="fas fa-chevron-right"></i> 卡密查询</a></li>
                <li><a href="/user/czpass.php"><i class="fas fa-chevron-right"></i> 找回密码</a></li>
                <li><a href="/user/agreement.php"><i class="fas fa-chevron-right"></i> 服务协议</a></li>
                <li><a href="/user/privacy.php"><i class="fas fa-chevron-right"></i> 隐私政策</a></li>
              </ul>
            </div>
          </div>
          
          <div class="col-lg-3 col-md-6">
            <div class="footer-widget contact-widget">
              <h4 class="footer-title">联系我们</h4>
              <div class="contact-info-item">
                <div class="contact-info-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="contact-info-text">
                  <span>中国·深圳</span>
                </div>
              </div>
              <div class="contact-info-item">
                <div class="contact-info-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-info-text">
                  <span><EMAIL></span>
                </div>
              </div>
              <div class="contact-info-item">
                <div class="contact-info-icon">
                  <i class="fas fa-phone-alt"></i>
                </div>
                <div class="contact-info-text">
                  <span>+86 755 1234 5678</span>
                </div>
              </div>
              <div class="footer-newsletter">
                <h5>订阅我们的通讯</h5>
                <div class="newsletter-form">
                  <input type="email" placeholder="您的邮箱地址">
                  <button type="submit"><i class="fas fa-paper-plane"></i></button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="footer-bottom">
      <div class="container">
        <div class="footer-bottom-content">
          <div class="copyright">
            &copy; <?=date('Y')?> <a href="/"><?php echo $conf['sitename']?></a> - 懂你的才是最好的
          </div>
          <div class="footer-bottom-links">
            <a href="/user/agreement.php">服务协议</a>
            <a href="/user/privacy.php">隐私政策</a>
            <a href="/sitemap.xml">网站地图</a>
          </div>
        </div>
        <div class="site-runtime text-center mt-3">
          <div class="runtime-container">
            <div class="runtime-icon"><i class="fas fa-heartbeat pulse"></i></div>
            <div class="runtime-text">本站已稳定运行 <span id="runtime-counter" class="fw-bold"></span></div>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- 客服按钮 -->
  <div class="customer-service" id="customer-service">
    <i class="fas fa-headset"></i>
  </div>

  <!-- 通知弹窗 -->
  <div class="notice-modal" id="notice-modal">
    <div class="notice-header">
      <h3 class="notice-title">网站通知</h3>
    </div>
    <div class="notice-body">
      <p class="notice-content"><?php echo $conf['index_notice'] ?></p>
      <div class="notice-actions">
        <a href="<?php echo $conf['Communication'] ?>" target="_blank" class="notice-btn notice-btn-primary">加入QQ群</a>
        <button class="notice-btn notice-btn-secondary" id="close-notice">我知道了</button>
      </div>
    </div>
  </div>

  <!-- JavaScript -->
  <script>
    // 初始化AOS动画库
    document.addEventListener('DOMContentLoaded', function() {
      AOS.init({
        duration: 800,
        easing: 'ease-in-out',
        once: true,
        mirror: false
      });
      
      // 背景动画
      initBackgroundAnimation();
      
      // 3D效果
      if (document.getElementById('hero-3d-container')) {
        initThreeJS();
      }
      
      // 显示通知弹窗
      setTimeout(() => {
        document.getElementById('notice-modal').classList.add('show');
      }, 1500);
      
      // 关闭通知弹窗
      document.getElementById('close-notice').addEventListener('click', function() {
        document.getElementById('notice-modal').classList.remove('show');
      });
      
      // 客服按钮点击
      document.getElementById('customer-service').addEventListener('click', function() {
        window.open('<?php echo $conf["Communication"] ?>', '_blank');
      });
      
      // 心形点击效果
      initHeartClickEffect();
      
      // 运行时间计算
      updateRuntime();
      setInterval(updateRuntime, 1000);
      
      // 更新当前时间
      updateCurrentTime();
      setInterval(updateCurrentTime, 1000);
      
      // API调用数字动画
      animateAPICounter();
      
      // FPS计数器
      initFPSCounter();
    });
    
    // 背景动画初始化
    function initBackgroundAnimation() {
      const floatingShapes = document.getElementById('floating-shapes');
      for (let i = 0; i < 8; i++) {
        const shape = document.createElement('div');
        shape.classList.add('shape');
        shape.style.width = Math.random() * 300 + 100 + 'px';
        shape.style.height = shape.style.width;
        shape.style.left = Math.random() * 100 + '%';
        shape.style.top = Math.random() * 100 + '%';
        floatingShapes.appendChild(shape);
        
        // 随机动画
        animateShape(shape);
      }
    }
    
    function animateShape(shape) {
      const duration = Math.random() * 30 + 20;
      const xMove = Math.random() * 20 - 10;
      const yMove = Math.random() * 20 - 10;
      
      gsap.to(shape, {
        x: xMove + '%',
        y: yMove + '%',
        duration: duration,
        ease: 'sine.inOut',
        repeat: -1,
        yoyo: true
      });
    }
    
    // Three.js 3D效果
    function initThreeJS() {
      const container = document.getElementById('hero-3d-container');
      const width = container.clientWidth;
      const height = container.clientHeight || 400;
      
      // 创建场景
      const scene = new THREE.Scene();
      const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
      const renderer = new THREE.WebGLRenderer({ alpha: true, antialias: true });
      
      renderer.setSize(width, height);
      renderer.setPixelRatio(window.devicePixelRatio);
      container.appendChild(renderer.domElement);
      
      // 创建几何体
      const geometry = new THREE.TorusKnotGeometry(10, 3, 100, 16);
      const material = new THREE.MeshBasicMaterial({ 
        color: 0x0071e3,
        wireframe: true
      });
      
      const torusKnot = new THREE.Mesh(geometry, material);
      scene.add(torusKnot);
      
      camera.position.z = 30;
      
      // 动画
      function animate() {
        requestAnimationFrame(animate);
        
        torusKnot.rotation.x += 0.01;
        torusKnot.rotation.y += 0.01;
        
        renderer.render(scene, camera);
      }
      
      animate();
      
      // 响应窗口大小变化
      window.addEventListener('resize', function() {
        const newWidth = container.clientWidth;
        const newHeight = container.clientHeight || 400;
        
        camera.aspect = newWidth / newHeight;
        camera.updateProjectionMatrix();
        
        renderer.setSize(newWidth, newHeight);
      });
    }
    
    // 心形点击效果
    function initHeartClickEffect() {
      document.addEventListener('click', function(e) {
        const hearts = ["❤富强❤", "❤民主❤", "❤文明❤", "❤和谐❤", "❤自由❤", "❤平等❤", "❤公正❤", "❤法治❤", "❤爱国❤", "❤敬业❤", "❤诚信❤", "❤友善❤"];
        const colors = ["#ff5e5e", "#5e8fff", "#5effbc", "#bc5eff", "#ffbc5e", "#ff5e8f", "#5effff"];
        
        const heart = document.createElement('span');
        heart.classList.add('heart-animation');
        heart.innerText = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.color = colors[Math.floor(Math.random() * colors.length)];
        heart.style.left = e.pageX + 'px';
        heart.style.top = e.pageY + 'px';
        
        document.body.appendChild(heart);
        
        setTimeout(() => {
          heart.remove();
        }, 1500);
      });
    }
    
    // 运行时间计算
    function updateRuntime() {
      const startDate = new Date("3/3/2024 00:00:00");
      const now = new Date();
      const timeDiff = now.getTime() - startDate.getTime();
      
      const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);
      
      document.getElementById('runtime-counter').innerHTML = 
        `<span style="color:#0071e3">${days}</span> 天 
         <span style="color:#0071e3">${hours}</span> 时 
         <span style="color:#0071e3">${minutes}</span> 分 
         <span style="color:#0071e3">${seconds}</span> 秒`;
    }
    
    // 更新当前时间
    function updateCurrentTime() {
      const now = new Date();
      const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric', 
        hour: '2-digit', 
        minute: '2-digit', 
        second: '2-digit',
        hour12: false
      };
      document.getElementById('current-time').textContent = now.toLocaleDateString('zh-CN', options);
    }
    
    // API调用数字动画
    function animateAPICounter() {
      const apiCountElement = document.getElementById('api-count');
      const finalCount = 9382651;
      let currentCount = 9380000;
      
      function updateApiCount() {
        if (currentCount < finalCount) {
          currentCount += Math.floor(Math.random() * 10) + 1;
          if (currentCount > finalCount) currentCount = finalCount;
          apiCountElement.textContent = currentCount.toLocaleString('en-US');
          setTimeout(updateApiCount, 100);
        }
      }
      
      updateApiCount();
    }
    
    // FPS计数器
    function initFPSCounter() {
      let fps = 0;
      let lastTime = performance.now();
      const fpsDisplay = document.getElementById('fps-display');
      
      function updateFPS() {
        const now = performance.now();
        fps++;
        
        if (now - lastTime >= 1000) {
          fpsDisplay.textContent = fps + ' FPS';
          fps = 0;
          lastTime = now;
        }
        
        requestAnimationFrame(updateFPS);
      }
      
      updateFPS();
    }
  </script>
</body>
</html>